import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/auth/data/models/user_model.dart';
import 'package:flutter_demo/features/profile/presentation/screens/gender_selection_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PersonalInfoScreen extends StatefulWidget {
  const PersonalInfoScreen({Key? key}) : super(key: key);

  @override
  State<PersonalInfoScreen> createState() => _PersonalInfoScreenState();
}

class _PersonalInfoScreenState extends State<PersonalInfoScreen> {
  // 性别选择
  int _selectedGender = 0; // 0表示男性，1表示女性

  // 头像图片
  File? _avatarImage;
  final ImagePicker _imagePicker = ImagePicker();

  // 用户信息
  String _userName = '';
  String _userPhone = '';
  String _userCode = '';
  String _deptName = '';
  String _dept = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  /// 加载用户信息
  Future<void> _loadUserInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(AppConstants.userKey);

      if (userJson != null) {
        final user = UserModel.fromJson(json.decode(userJson));
        setState(() {
          _userName = user.userName ?? '';
          _userPhone = user.phone ?? '';
          _userCode = user.userCode ?? '';
          _deptName = user.deptName ?? '';
          _dept = user.dept ?? '';
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('加载用户信息失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 从相机拍照
  Future<void> _takePhoto() async {
    try {
      final XFile? photo = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80, // 图片质量
        maxWidth: 800,    // 最大宽度
        maxHeight: 800,   // 最大高度
      );

      if (photo != null) {
        setState(() {
          _avatarImage = File(photo.path);
        });
        AppSnackBar.showSuccess(context, '拍照成功');
      }
    } catch (e) {
      AppSnackBar.showError(context, '拍照失败: $e');
    }
  }

  // 从相册选择照片
  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80, // 图片质量
        maxWidth: 800,    // 最大宽度
        maxHeight: 800,   // 最大高度
      );

      if (image != null) {
        setState(() {
          _avatarImage = File(image.path);
        });
        AppSnackBar.showSuccess(context, '选择照片成功');
      }
    } catch (e) {
      AppSnackBar.showError(context, '选择照片失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: '个人信息',

        actions: [
          TextButton(
            onPressed: () {
              // 保存信息
              AppSnackBar.showSuccess(context, '信息已保存');
              Navigator.of(context).pop();
            },
            child: Text(
              '保存',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: 32.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 头像部分
            _buildAvatarSection(),

            // 信息表单部分
            _buildInfoField(
              label: '姓名',
              value: '周平樟',
            ),
            _buildGenderSelector(),
            _buildInfoField(
              label: '手机号',
              value: '18898617279',
              hasArrow: true,
              onTap: () {
                // 跳转到手机号修改页面
              },
            ),
            SizedBox(height: 10.h),
            _buildInfoField(
              label: '学校',
              value: '武汉大学',
            ),
            _buildInfoField(
              label: '学院',
              value: '计算机学院',
            ),
            _buildInfoField(
              label: '工号',
              value: '1365876',
            ),
          ],
        ),
      ),
    );
  }

  // 头像部分
  Widget _buildAvatarSection() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 25.w),
      child: _buildInfoFieldWithAvatar(
        label: '头像',
        onTap: () {
          // 点击修改头像
          _showAvatarSelectionBottomSheet();
        },
      ),
    );
  }

  // 显示头像选择底部对话框
  void _showAvatarSelectionBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(0)),
      ),
      builder: (BuildContext context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildBottomSheetOption(
              title: '拍照',
              onTap: () {
                Navigator.pop(context);
                _takePhoto();
              },
            ),
            Divider(
              thickness: 1,
              color: Colors.grey[300],
            ),
            _buildBottomSheetOption(
              title: '从相册中选择',
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery();
              },
            ),
            Container(
              height: 15.h,
              color: Colors.grey[200],
            ),
            _buildBottomSheetOption(
              title: '取消',
              onTap: () {
                Navigator.pop(context);
              },
            ),
          ],
        );
      },
    );
  }

  // 构建底部对话框选项
  Widget _buildBottomSheetOption({
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 35.h),
        alignment: Alignment.center,
        child: Text(
          title,
          style: TextStyle(
            fontSize: 28.sp,
            color: AppTheme.black333,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  // 带头像的信息字段
  Widget _buildInfoFieldWithAvatar({
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 20.h),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[200]!,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            // 标签
            Text(
              label,
              style: TextStyle(
                fontSize: 30.sp,
                color: AppTheme.black999,
              ),
            ),
            const Spacer(),
            // 头像
            CircleAvatar(
              radius: 35.w,
              backgroundColor: Colors.blue[100],
              backgroundImage: _avatarImage != null
                ? FileImage(_avatarImage!) as ImageProvider
                : const NetworkImage(AppConstants.avatar1),
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                ),
              ),
            ),
            SizedBox(width: 10.w),
            // 箭头
            Icon(
              Icons.arrow_forward_ios,
              size: 28.sp,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  // 信息表单字段
  Widget _buildInfoField({
    required String label,
    required String value,
    bool isEditable = false,
    bool hasArrow = false,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[200]!,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            // 标签
            Text(
              label,
              style: TextStyle(
                fontSize: 30.sp,
                color: AppTheme.black999,
              ),
            ),
            // 值
            Expanded(
              child: Text(
                value,
                textAlign: TextAlign.right,
                style: TextStyle(
                  fontSize: 30.sp,
                  color: AppTheme.black333,
                ),
              ),
            ),
            // 箭头
            if (hasArrow)
              Padding(
                padding: EdgeInsets.only(left: 10.w),
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 28.sp,
                  color: Colors.grey[400],
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 性别选择器
  Widget _buildGenderSelector() {
    return GestureDetector(
      onTap: () {
        // 跳转到性别选择页面
        _navigateToGenderSelectionScreen();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[200]!,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            // 标签
            Text(
              '性别',
              style: TextStyle(
                fontSize: 30.sp,
                color: AppTheme.black999,
              ),
            ),
            // 值
            Expanded(
              child: Text(
                _selectedGender == 0 ? '男' : '女',
                textAlign: TextAlign.right,
                style: TextStyle(
                  fontSize: 30.sp,
                  color: AppTheme.black333,
                ),
              ),
            ),
            // 箭头
            Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: Icon(
                Icons.arrow_forward_ios,
                size: 28.sp,
                color: Colors.grey[400],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 跳转到性别选择页面
  Future<void> _navigateToGenderSelectionScreen() async {
    // 导入需要在方法内部进行，避免循环依赖
    // ignore: unused_local_variable
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GenderSelectionScreen(
          initialGender: _selectedGender,
        ),
      ),
    );

    // 如果返回了结果，更新性别
    if (result != null && result is int) {
      setState(() {
        _selectedGender = result;
      });
    }
  }


}